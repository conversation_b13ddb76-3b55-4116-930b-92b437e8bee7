"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/CitationTooltip.tsx":
/*!********************************************!*\
  !*** ./app/components/CitationTooltip.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CitationTooltip: function() { return /* binding */ CitationTooltip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ CitationTooltip auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CitationTooltip(param) {\n    let { citationId, children } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [citationData, setCitationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const triggerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const loadCitationData = async ()=>{\n        if (citationData || isLoading) return;\n        setIsLoading(true);\n        try {\n            var _data_metadata;\n            const response = await fetch(\"/api/citation/\".concat(citationId));\n            if (!response.ok) throw new Error(\"Failed to load citation\");\n            const data = await response.json();\n            setCitationData({\n                id: data.id || citationId,\n                title: ((_data_metadata = data.metadata) === null || _data_metadata === void 0 ? void 0 : _data_metadata.file_name) || \"Document \".concat(citationId.substring(0, 8), \"...\"),\n                content: data.text || data.content || \"内容不可用\",\n                metadata: data.metadata\n            });\n        } catch (error) {\n            console.error(\"Error loading citation:\", error);\n            setCitationData({\n                id: citationId,\n                title: \"Error\",\n                content: \"Failed to load citation content\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleMouseEnter = (e)=>{\n        var _triggerRef_current;\n        const rect = (_triggerRef_current = triggerRef.current) === null || _triggerRef_current === void 0 ? void 0 : _triggerRef_current.getBoundingClientRect();\n        if (rect) {\n            setPosition({\n                x: rect.left + rect.width / 2,\n                y: rect.top - 10\n            });\n        }\n        setIsVisible(true);\n        loadCitationData();\n    };\n    const handleMouseLeave = ()=>{\n        setIsVisible(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && tooltipRef.current) {\n            const tooltip = tooltipRef.current;\n            const rect = tooltip.getBoundingClientRect();\n            const viewportWidth = window.innerWidth;\n            const viewportHeight = window.innerHeight;\n            let adjustedX = position.x - rect.width / 2;\n            let adjustedY = position.y - rect.height;\n            // 确保tooltip不超出视口\n            if (adjustedX < 10) adjustedX = 10;\n            if (adjustedX + rect.width > viewportWidth - 10) {\n                adjustedX = viewportWidth - rect.width - 10;\n            }\n            if (adjustedY < 10) adjustedY = position.y + 30;\n            tooltip.style.left = \"\".concat(adjustedX, \"px\");\n            tooltip.style.top = \"\".concat(adjustedY, \"px\");\n        }\n    }, [\n        isVisible,\n        position.x,\n        position.y,\n        citationData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                ref: triggerRef,\n                onMouseEnter: handleMouseEnter,\n                onMouseLeave: handleMouseLeave,\n                className: \"citation-number\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: tooltipRef,\n                className: \"citation-tooltip fixed z-50\",\n                style: {\n                    left: position.x,\n                    top: position.y\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                            size: \"sm\",\n                            className: \"text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 13\n                }, this) : citationData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-gray-900 mb-2\",\n                            children: citationData.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-700 text-sm leading-relaxed\",\n                            children: citationData.content.length > 300 ? \"\".concat(citationData.content.substring(0, 300), \"...\") : citationData.content\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Error loading citation\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CitationTooltip, \"4AkT3amWGgzvqGUzLBx2HAlaYpA=\");\n_c = CitationTooltip;\nvar _c;\n$RefreshReg$(_c, \"CitationTooltip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/CitationTooltip.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/CustomChatMessage.tsx":
/*!**********************************************!*\
  !*** ./app/components/CustomChatMessage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomChatMessage: function() { return /* binding */ CustomChatMessage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChatMessage_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChatMessage!=!@llamaindex/chat-ui */ \"(app-pages-browser)/./node_modules/@llamaindex/chat-ui/dist/chat/index.js\");\n/* harmony import */ var _CitationTooltip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CitationTooltip */ \"(app-pages-browser)/./app/components/CitationTooltip.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ CustomChatMessage auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CustomChatMessage(param) {\n    let { message, isLoading } = param;\n    _s();\n    // 使用 useMemo 优化处理引用标记的函数，避免每次渲染都重新计算\n    const processContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return (content)=>{\n            // 查找所有引用标记\n            const citationRegex = /\\[citation:(.*?)\\]/g;\n            const citations = [];\n            let match;\n            while((match = citationRegex.exec(content)) !== null){\n                citations.push(match[1]);\n            }\n            // 替换引用标记为带tooltip的序号\n            let processedContent = content;\n            citations.forEach((citationId, index)=>{\n                const citationNumber = index + 1;\n                const citationPattern = new RegExp(\"\\\\[citation:\".concat(citationId.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \"\\\\]\"), \"g\");\n                processedContent = processedContent.replace(citationPattern, '<citation-placeholder data-citation-id=\"'.concat(citationId, '\" data-citation-number=\"').concat(citationNumber, '\"></citation-placeholder>'));\n            });\n            return processedContent;\n        };\n    }, []);\n    // 使用 useMemo 优化渲染处理后的内容\n    const renderContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return (content)=>{\n            const processedContent = processContent(content);\n            // 分割内容并处理引用占位符\n            const parts = processedContent.split(/(<citation-placeholder[^>]*><\\/citation-placeholder>)/g);\n            return parts.map((part, index)=>{\n                const citationMatch = part.match(/<citation-placeholder data-citation-id=\"([^\"]*)\" data-citation-number=\"([^\"]*)\"><\\/citation-placeholder>/);\n                if (citationMatch) {\n                    const [, citationId, citationNumber] = citationMatch;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CitationTooltip__WEBPACK_IMPORTED_MODULE_1__.CitationTooltip, {\n                        citationId: citationId,\n                        children: [\n                            \"[\",\n                            citationNumber,\n                            \"]\"\n                        ]\n                    }, \"citation-\".concat(index), true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, this);\n                }\n                return part;\n            });\n        };\n    }, [\n        processContent\n    ]);\n    // 如果是助手消息且包含引用，使用自定义渲染\n    if (message.role === \"assistant\" && message.content.includes(\"[citation:\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\", \" mb-4 chat-message\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[80%] rounded-lg px-4 py-3 \".concat(message.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"bg-muted text-muted-foreground border border-border\", \" shadow-sm\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-sm max-w-none dark:prose-invert\",\n                        children: renderContent(message.content)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-3 space-x-2 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs\",\n                                children: \"正在思考...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    // 对于其他消息，使用默认的ChatMessage组件\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatMessage_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__.ChatMessage, {\n        message: message,\n        isLoading: isLoading\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n        lineNumber: 107,\n        columnNumber: 10\n    }, this);\n}\n_s(CustomChatMessage, \"42MNKg14tSt5NIOut1GKAGGzxdc=\");\n_c = CustomChatMessage;\nvar _c;\n$RefreshReg$(_c, \"CustomChatMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/CustomChatMessage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/IsolatedChatPage.tsx":
/*!*********************************************!*\
  !*** ./app/components/IsolatedChatPage.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ IsolatedChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CustomChatMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CustomChatMessage */ \"(app-pages-browser)/./app/components/CustomChatMessage.tsx\");\n/* harmony import */ var _ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ErrorBoundary */ \"(app-pages-browser)/./app/components/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction IsolatedChatPage() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"您好！我是您的AI助手，可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？\"\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (content)=>{\n        if (!content.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: content.trim()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        // 取消之前的请求\n        if (abortControllerRef.current) {\n            abortControllerRef.current.abort();\n        }\n        abortControllerRef.current = new AbortController();\n        try {\n            var _response_body;\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    id: \"chat-\".concat(Date.now()),\n                    messages: [\n                        ...messages,\n                        userMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content\n                        }))\n                }),\n                signal: abortControllerRef.current.signal\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();\n            if (!reader) {\n                throw new Error(\"No response body\");\n            }\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            const decoder = new TextDecoder();\n            let buffer = \"\";\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                buffer += decoder.decode(value, {\n                    stream: true\n                });\n                const lines = buffer.split(\"\\n\");\n                buffer = lines.pop() || \"\";\n                for (const line of lines){\n                    if (line.startsWith(\"data: \")) {\n                        const data = line.slice(6);\n                        if (data === \"[DONE]\") continue;\n                        try {\n                            var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                            const parsed = JSON.parse(data);\n                            if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                const content = parsed.choices[0].delta.content;\n                                setMessages((prev)=>{\n                                    const newMessages = [\n                                        ...prev\n                                    ];\n                                    const lastMessage = newMessages[newMessages.length - 1];\n                                    if (lastMessage.role === \"assistant\") {\n                                        lastMessage.content += content;\n                                    }\n                                    return newMessages;\n                                });\n                            }\n                        } catch (e) {\n                        // 忽略解析错误\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            if (error.name !== \"AbortError\") {\n                console.error(\"Chat error:\", error);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: (Date.now() + 1).toString(),\n                            role: \"assistant\",\n                            content: \"抱歉，发生了错误。请稍后重试。\"\n                        }\n                    ]);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        sendMessage(input);\n    }, [\n        input,\n        sendMessage\n    ]);\n    const handleKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage(input);\n        }\n    }, [\n        input,\n        sendMessage\n    ]);\n    // 清理函数\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n            }\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                    children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CustomChatMessage__WEBPACK_IMPORTED_MODULE_2__.CustomChatMessage, {\n                            message: message,\n                            isLoading: isLoading && message === messages[messages.length - 1]\n                        }, message.id, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\IsolatedChatPage.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\IsolatedChatPage.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-border p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"输入您的问题...\",\n                                className: \"flex-1 min-h-[44px] max-h-32 px-3 py-2 border border-border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\IsolatedChatPage.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: !input.trim() || isLoading,\n                                className: \"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isLoading ? \"发送中...\" : \"发送\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\IsolatedChatPage.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\IsolatedChatPage.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\IsolatedChatPage.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\IsolatedChatPage.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\IsolatedChatPage.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n_s(IsolatedChatPage, \"GItQucU/3kY2KfKW7wAmTjytjY4=\");\n_c = IsolatedChatPage;\nvar _c;\n$RefreshReg$(_c, \"IsolatedChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/IsolatedChatPage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/LoadingSpinner.tsx":
/*!*******************************************!*\
  !*** ./app/components/LoadingSpinner.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: function() { return /* binding */ LoadingSpinner; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ LoadingSpinner auto */ \nfunction LoadingSpinner(param) {\n    let { size = \"md\", className = \"\" } = param;\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-6 h-6\",\n        lg: \"w-8 h-8\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(sizeClasses[size], \" border-2 border-current border-t-transparent rounded-full animate-spin \").concat(className)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\LoadingSpinner.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_c = LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9jb21wb25lbnRzL0xvYWRpbmdTcGlubmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFPTyxTQUFTQSxlQUFlLEtBQW9EO1FBQXBELEVBQUVDLE9BQU8sSUFBSSxFQUFFQyxZQUFZLEVBQUUsRUFBdUIsR0FBcEQ7SUFDN0IsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLHFCQUNFLDhEQUFDQztRQUFJTCxXQUFXLEdBQStGQSxPQUE1RkMsV0FBVyxDQUFDRixLQUFLLEVBQUMsNEVBQW9GLE9BQVZDOzs7Ozs7QUFFbkg7S0FWZ0JGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9jb21wb25lbnRzL0xvYWRpbmdTcGlubmVyLnRzeD84NGRiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbnRlcmZhY2UgTG9hZGluZ1NwaW5uZXJQcm9wcyB7XG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZydcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMb2FkaW5nU3Bpbm5lcih7IHNpemUgPSAnbWQnLCBjbGFzc05hbWUgPSAnJyB9OiBMb2FkaW5nU3Bpbm5lclByb3BzKSB7XG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtOiAndy00IGgtNCcsXG4gICAgbWQ6ICd3LTYgaC02JywgXG4gICAgbGc6ICd3LTggaC04J1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YCR7c2l6ZUNsYXNzZXNbc2l6ZV19IGJvcmRlci0yIGJvcmRlci1jdXJyZW50IGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gJHtjbGFzc05hbWV9YH0gLz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsImNsYXNzTmFtZSIsInNpemVDbGFzc2VzIiwic20iLCJtZCIsImxnIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/LoadingSpinner.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_IsolatedChatPage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/IsolatedChatPage */ \"(app-pages-browser)/./app/components/IsolatedChatPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ChatPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_IsolatedChatPage__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRTZEO0FBRTlDLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCxvRUFBZ0JBOzs7OztBQUMxQjtLQUZ3QkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3BhZ2UudHN4Pzc2MDMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBJc29sYXRlZENoYXRQYWdlIGZyb20gXCIuL2NvbXBvbmVudHMvSXNvbGF0ZWRDaGF0UGFnZVwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDaGF0UGFnZSgpIHtcbiAgcmV0dXJuIDxJc29sYXRlZENoYXRQYWdlIC8+O1xufVxuIl0sIm5hbWVzIjpbIklzb2xhdGVkQ2hhdFBhZ2UiLCJDaGF0UGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});